/**
 * Utilities for computing image sharpness using the variance of the Laplacian.
 * This is a pure JavaScript implementation that operates on ImageData (e.g., from a Canvas).
 */

/**
 * Convert RGBA image data to a grayscale intensity array.
 * @param imageData - The ImageData object containing RGBA pixel data.
 * @returns A Uint8ClampedArray of grayscale intensities.
 */
export function toGrayscale(imageData: ImageData): Uint8ClampedArray {
  const { data, width, height } = imageData;
  const gray = new Uint8ClampedArray(width * height);
  for (let i = 0; i < width * height; i++) {
    const r = data[i * 4];
    const g = data[i * 4 + 1];
    const b = data[i * 4 + 2];
    // Standard luminance conversion
    gray[i] = 0.299 * r + 0.587 * g + 0.114 * b;
  }
  return gray;
}

/**
 * Compute the variance of the Laplacian of a grayscale image.
 * @param gray - Grayscale intensity array (width * height elements).
 * @param width - The width of the image.
 * @param height - The height of the image.
 * @returns The variance of the Laplacian values, which correlates with image sharpness.
 */
export function calculateLaplacianVarianceFromGray(
  gray: Uint8ClampedArray,
  width: number,
  height: number
): number {
  const laplacianCount = (width - 2) * (height - 2);
  let sum = 0;
  let sumSq = 0;
  // 3x3 Laplacian kernel: [0, 1, 0; 1, -4, 1; 0, 1, 0]
  const kernel = [0, 1, 0, 1, -4, 1, 0, 1, 0];

  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      let idx = 0;
      let lapVal = 0;
      for (let ky = -1; ky <= 1; ky++) {
        for (let kx = -1; kx <= 1; kx++) {
          const pixel = gray[(y + ky) * width + (x + kx)];
          lapVal += kernel[idx++] * pixel;
        }
      }
      sum += lapVal;
      sumSq += lapVal * lapVal;
    }
  }

  const mean = sum / laplacianCount;
  return sumSq / laplacianCount - mean * mean;
}

/**
 * Convenience wrapper: calculates Laplacian variance directly from ImageData.
 * @param imageData - The ImageData object to analyze.
 * @returns The Laplacian variance (sharpness metric).
 */
export function calculateLaplacianVariance(imageData: ImageData): number {
  const gray = toGrayscale(imageData);
  return calculateLaplacianVarianceFromGray(gray, imageData.width, imageData.height);
}
